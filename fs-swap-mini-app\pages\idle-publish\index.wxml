<!--pages/idle-publish/index.wxml-->
<view class="container">
  <form bindsubmit="handleSubmit">
    <view class="content-card">
      <view class="description-area">
        <textarea 
          name="description" 
          placeholder="描述宝贝的品牌型号、功能成色、出手原因..." 
          value="{{ formData.description }}"
          bindinput="onDescriptionChange"
        />
      </view>
      <view class="upload-section">
        <van-uploader 
          file-list="{{ formData.fileList }}" 
          deletable="{{ true }}"
          bind:after-read="afterRead"
          bind:delete="onDeleteFile"
          max-count="{{MAX_UPLOAD_COUNT}}"
          multiple="{{true}}"
          upload-text="添加图片"
          image-fit="aspectFill"
        />
      </view>
    </view>

    <view class="trade-card">
      <view class="condition-section" bindtap="showPriceInput">
        <text class="condition-label">价格</text>
        <view class="condition-value">
          <text>¥{{formData.price || '0.00'}}</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="condition-section" bindtap="showWearPicker">
        <text class="condition-label">成色</text>
        <view class="condition-value">
          <text>{{ formData.wearText || '请选择' }}</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="condition-section" bindtap="showContactPicker">
        <text class="condition-label">联系方式</text>
        <view class="condition-value">
          <view class="contact-icons">
            <van-icon name="phone" size="20px" color="{{ contactInfo.mobileVisible ? '#4080FF' : '#cccccc' }}" />
            <van-icon name="wechat" size="20px" color="{{ contactInfo.wechatVisible ? '#4080FF' : '#cccccc' }}" />
            <van-icon name="qr-invalid" size="20px" color="{{ contactInfo.wechatQrVisible ? '#4080FF' : '#cccccc' }}" />
          </view>
          <text class="arrow">></text>
        </view>
      </view>
      <!-- 联系方式提示信息 -->
      <view class="contact-tip" wx:if="{{ !hasValidContact }}">
        <van-icon name="info-o" size="14px" color="#ff6b6b" />
        <text>请至少维护一种联系方式</text>
      </view>
    </view>

    <view class="submit-section">
      <button
        form-type="submit"
        class="publish-btn"
        loading="{{ publishing }}"
        disabled="{{ !formValid }}"
      >发布</button>
    </view>
  </form>

  <!-- 成色选择器 -->
  <van-popup
    show="{{ showWearPicker }}"
    position="bottom"
    round
    bind:close="onCloseWearPicker"
  >
    <van-picker
      show-toolbar
      title="选择成色"
      columns="{{ wearOptions }}"
      bind:cancel="onCloseWearPicker"
      bind:confirm="onSelectWear"
      default-index="{{ wearIndex }}"
    />
  </van-popup>

  <!-- 价格输入弹窗 -->
  <van-popup
    show="{{showPriceInput}}"
    position="bottom"
    round
    bind:close="onClosePriceInput"
    custom-style="background: #f7f8fa;"
  >
    <view class="price-input-popup">
      <view class="price-display">
        <text class="price-label">价格</text>
        <view class="value">
          <text class="currency">¥</text>
          <text class="price-value">{{formData.price || '0.00'}}</text>
        </view>
      </view>
      <view class="keyboard-container">
        <view class="keyboard-wrapper">
          <view class="keyboard-left">
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key="1">1</view>
              <view class="key-item" bindtap="onKeyPress" data-key="2">2</view>
              <view class="key-item" bindtap="onKeyPress" data-key="3">3</view>
            </view>
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key="4">4</view>
              <view class="key-item" bindtap="onKeyPress" data-key="5">5</view>
              <view class="key-item" bindtap="onKeyPress" data-key="6">6</view>
            </view>
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key="7">7</view>
              <view class="key-item" bindtap="onKeyPress" data-key="8">8</view>
              <view class="key-item" bindtap="onKeyPress" data-key="9">9</view>
            </view>
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key=".">.</view>
              <view class="key-item" bindtap="onKeyPress" data-key="0">0</view>
              <view class="key-item" bindtap="onClosePriceInput">
                <van-icon name="arrow-down" size="36rpx" />
              </view>
            </view>
          </view>
          <view class="keyboard-right">
            <view class="key-item delete" bindtap="onDelete">
              <van-icon name="cross" size="36rpx" />
            </view>
            <view class="key-item confirm" bindtap="onConfirmPrice">确定</view>
          </view>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 引入小区认证弹窗组件 -->
  <residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />

  <!-- 联系方式选择弹窗 -->
  <van-popup
    show="{{ showContactPicker }}"
    position="bottom"
    round
    bind:close="onCloseContactPicker"
    custom-style="background: #f7f8fa;"
  >
    <view class="contact-picker-popup">
      <view class="contact-picker-header">
        <text class="contact-picker-title">联系方式设置</text>
        <van-icon name="cross" size="18px" class="close-icon" bindtap="onCloseContactPicker" />
      </view>
      <view class="contact-picker-content">
        <view class="contact-item">
          <view class="contact-item-left" bindtap="showMobileDialog">
            <van-icon name="phone" size="24px" color="{{ contactInfo.mobileVisible ? '#4080FF' : '#cccccc' }}" />
            <view class="contact-item-info">
              <text class="contact-item-label">手机号</text>
              <text class="contact-item-value">{{ contactInfo.mobile || '点击设置' }}</text>
            </view>
          </view>
          <view class="contact-item-right">
            <van-switch 
              checked="{{ contactInfo.mobileVisible }}" 
              size="24px" 
              bind:change="onMobileVisibilityChange"
              wx:if="{{ contactInfo.mobile }}"
              catch:tap="preventTap"
            />
            <text class="contact-item-status" wx:if="{{ !contactInfo.mobile }}">未设置</text>
          </view>
        </view>
        <view class="contact-item-divider"></view>
        <view class="contact-item">
          <view class="contact-item-left" bindtap="showWechatDialog">
            <van-icon name="wechat" size="24px" color="{{ contactInfo.wechatVisible ? '#4080FF' : '#cccccc' }}" />
            <view class="contact-item-info">
              <text class="contact-item-label">微信号</text>
              <text class="contact-item-value">{{ contactInfo.wechatId || '点击设置' }}</text>
            </view>
          </view>
          <view class="contact-item-right">
            <van-switch 
              checked="{{ contactInfo.wechatVisible }}" 
              size="24px" 
              bind:change="onWechatVisibilityChange"
              wx:if="{{ contactInfo.wechatId }}"
              catch:tap="preventTap"
            />
            <text class="contact-item-status" wx:if="{{ !contactInfo.wechatId }}">未设置</text>
          </view>
        </view>
        <view class="contact-item-divider"></view>
        <view class="contact-item">
          <view class="contact-item-left" bindtap="showWechatQrDialog">
            <van-icon name="qr-invalid" size="24px" color="{{ contactInfo.wechatQrVisible ? '#4080FF' : '#cccccc' }}" />
            <view class="contact-item-info">
              <text class="contact-item-label">微信二维码</text>
              <view class="contact-item-value-container">
                <text class="contact-item-value" wx:if="{{ !contactInfo.wechatQr }}">点击设置</text>
                <image wx:else class="qr-code-mini-preview" src="{{ contactInfo.wechatQr }}" mode="aspectFit" />
              </view>
            </view>
          </view>
          <view class="contact-item-right">
            <van-switch 
              checked="{{ contactInfo.wechatQrVisible }}" 
              size="24px" 
              bind:change="onWechatQrVisibilityChange"
              wx:if="{{ contactInfo.wechatQr }}"
              catch:tap="preventTap"
            />
            <text class="contact-item-status" wx:if="{{ !contactInfo.wechatQr }}">未设置</text>
          </view>
        </view>
      </view>
      <view class="contact-picker-footer">
        <view class="contact-picker-tip">
          <van-icon name="info-o" size="14px" color="#999" />
          <text>设置后买家可通过您选择的联系方式与您联系</text>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 手机号编辑弹窗 -->
  <van-dialog
    use-slot
    title="修改手机号"
    show="{{ showMobileDialog }}"
    show-cancel-button
    bind:close="onCloseMobileDialog"
    bind:confirm="onConfirmMobile"
    cancel-button-text="取消"
    confirm-button-text="确定"
    confirm-button-color="#4080FF"
  >
    <view class="contact-edit-container">
      <van-field
        model:value="{{ mobileField }}"
        placeholder="请输入手机号"
        type="number"
        maxlength="11"
        border="{{ false }}"
        input-align="left"
      />
      <view class="contact-tips">请输入11位有效手机号码</view>
    </view>
  </van-dialog>

  <!-- 微信号编辑弹窗 -->
  <van-dialog
    use-slot
    title="修改微信号"
    show="{{ showWechatDialog }}"
    show-cancel-button
    bind:close="onCloseWechatDialog"
    bind:confirm="onConfirmWechat"
    cancel-button-text="取消"
    confirm-button-text="确定"
    confirm-button-color="#4080FF"
  >
    <view class="contact-edit-container">
      <van-field
        model:value="{{ wechatField }}"
        placeholder="请输入微信号"
        border="{{ false }}"
        input-align="left"
      />
      <view class="contact-tips">微信号为6-20个字符，只能包含字母、数字、下划线和减号</view>
    </view>
  </van-dialog>

  <!-- 微信二维码编辑弹窗 -->
  <van-dialog
    use-slot
    title="上传微信二维码"
    show="{{ showWechatQrDialog }}"
    show-cancel-button
    bind:close="onCloseWechatQrDialog"
    bind:confirm="onConfirmWechatQr"
    cancel-button-text="取消"
    confirm-button-text="确定"
    confirm-button-color="#4080FF"
  >
    <view class="qr-upload-container">
      <view class="qr-preview-wrapper" bindtap="chooseQrCode">
        <image wx:if="{{ tempQrCodePath }}" class="qr-preview" src="{{ tempQrCodePath }}" mode="aspectFit" />
        <view wx:else class="qr-placeholder">
          <van-icon name="photograph" size="48px" color="#ddd" />
          <text class="qr-placeholder-text">点击选择图片</text>
        </view>
      </view>
      <view class="contact-tips">请上传清晰的微信二维码图片，建议使用微信中的"我的二维码"</view>
    </view>
  </van-dialog>
</view> 