<!-- 联系方式管理组件 -->
<!-- 联系方式选择弹窗 -->
<van-popup
  show="{{ show }}"
  position="bottom"
  round
  bind:close="onClose"
  custom-style="background: #f7f8fa;"
>
  <view class="contact-picker-popup">
    <view class="contact-picker-header">
      <text class="contact-picker-title">联系方式设置</text>
      <van-icon name="cross" size="18px" class="close-icon" bindtap="onClose" />
    </view>
    <view class="contact-picker-content">
      <!-- 手机号 -->
      <view class="contact-item">
        <view class="contact-item-left" bindtap="showMobileDialog">
          <van-icon name="phone" size="24px" color="{{ contactInfo.mobileVisible ? '#4080FF' : '#cccccc' }}" />
          <view class="contact-item-info">
            <text class="contact-item-label">手机号</text>
            <text class="contact-item-value">{{ contactInfo.mobile || '点击设置' }}</text>
          </view>
        </view>
        <view class="contact-item-right">
          <van-switch 
            checked="{{ contactInfo.mobileVisible }}" 
            size="24px" 
            bind:change="onMobileVisibilityChange"
            wx:if="{{ contactInfo.mobile }}"
            catch:tap="preventTap"
          />
          <text class="contact-item-status" wx:if="{{ !contactInfo.mobile }}">未设置</text>
        </view>
      </view>
      
      <view class="contact-item-divider"></view>
      
      <!-- 微信号 -->
      <view class="contact-item">
        <view class="contact-item-left" bindtap="showWechatDialog">
          <van-icon name="wechat" size="24px" color="{{ contactInfo.wechatVisible ? '#4080FF' : '#cccccc' }}" />
          <view class="contact-item-info">
            <text class="contact-item-label">微信号</text>
            <text class="contact-item-value">{{ contactInfo.wechatId || '点击设置' }}</text>
          </view>
        </view>
        <view class="contact-item-right">
          <van-switch 
            checked="{{ contactInfo.wechatVisible }}" 
            size="24px" 
            bind:change="onWechatVisibilityChange"
            wx:if="{{ contactInfo.wechatId }}"
            catch:tap="preventTap"
          />
          <text class="contact-item-status" wx:if="{{ !contactInfo.wechatId }}">未设置</text>
        </view>
      </view>
      
      <view class="contact-item-divider"></view>
      
      <!-- 微信二维码 -->
      <view class="contact-item">
        <view class="contact-item-left" bindtap="showWechatQrDialog">
          <van-icon name="qr-invalid" size="24px" color="{{ contactInfo.wechatQrVisible ? '#4080FF' : '#cccccc' }}" />
          <view class="contact-item-info">
            <text class="contact-item-label">微信二维码</text>
            <view class="contact-item-value-container">
              <text class="contact-item-value" wx:if="{{ !contactInfo.wechatQr }}">点击设置</text>
              <view wx:else class="qr-preview-mini">
                <image src="{{ contactInfo.wechatQr }}" mode="aspectFit" class="qr-mini-image" />
                <text class="contact-item-value">已设置</text>
              </view>
            </view>
          </view>
        </view>
        <view class="contact-item-right">
          <van-switch 
            checked="{{ contactInfo.wechatQrVisible }}" 
            size="24px" 
            bind:change="onWechatQrVisibilityChange"
            wx:if="{{ contactInfo.wechatQr }}"
            catch:tap="preventTap"
          />
          <text class="contact-item-status" wx:if="{{ !contactInfo.wechatQr }}">未设置</text>
        </view>
      </view>
    </view>
    <view class="contact-picker-footer">
      <view class="contact-picker-tip">
        <van-icon name="info-o" size="14px" color="#999" />
        <text>设置后买家可通过您选择的联系方式与您联系</text>
      </view>
    </view>
  </view>
</van-popup>

<!-- 手机号编辑弹窗 -->
<van-dialog
  use-slot
  title="修改手机号"
  show="{{ showMobileDialog }}"
  show-cancel-button
  bind:close="onCloseMobileDialog"
  bind:confirm="onConfirmMobile"
  cancel-button-text="取消"
  confirm-button-text="确定"
  confirm-button-color="#4080FF"
>
  <view class="contact-edit-container">
    <van-field
      model:value="{{ mobileField }}"
      placeholder="请输入手机号"
      type="number"
      maxlength="11"
      border="{{ false }}"
      input-align="left"
    />
    <view class="contact-tips">请输入11位有效手机号码</view>
  </view>
</van-dialog>

<!-- 微信号编辑弹窗 -->
<van-dialog
  use-slot
  title="修改微信号"
  show="{{ showWechatDialog }}"
  show-cancel-button
  bind:close="onCloseWechatDialog"
  bind:confirm="onConfirmWechat"
  cancel-button-text="取消"
  confirm-button-text="确定"
  confirm-button-color="#4080FF"
>
  <view class="contact-edit-container">
    <van-field
      model:value="{{ wechatField }}"
      placeholder="请输入微信号"
      border="{{ false }}"
      input-align="left"
    />
    <view class="contact-tips">微信号为6-20个字符，只能包含字母、数字、下划线和减号</view>
  </view>
</van-dialog>

<!-- 微信二维码编辑弹窗 -->
<van-dialog
  use-slot
  title="上传微信二维码"
  show="{{ showWechatQrDialog }}"
  show-cancel-button
  bind:close="onCloseWechatQrDialog"
  bind:confirm="onConfirmWechatQr"
  cancel-button-text="取消"
  confirm-button-text="确定"
  confirm-button-color="#4080FF"
>
  <view class="qr-upload-container">
    <view class="qr-preview-wrapper" bindtap="chooseQrCode">
      <image wx:if="{{ tempQrCodePath }}" class="qr-preview" src="{{ tempQrCodePath }}" mode="aspectFit" />
      <view wx:else class="qr-placeholder">
        <van-icon name="photograph" size="48px" color="#ddd" />
        <text class="qr-placeholder-text">点击选择图片</text>
      </view>
    </view>
    <view class="contact-tips">请上传清晰的微信二维码图片，建议使用微信中的"我的二维码"</view>
  </view>
</van-dialog>
