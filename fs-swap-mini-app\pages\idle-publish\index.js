const util = require("../../utils/util")
const api = require("../../config/api")
const userUtils = require("../../utils/user")
const app = getApp()
const systemInfoService = require('../../services/systemInfo.js')
const ContactType = require('../../utils/ContactType.js')

Page({
  data: {
    // 是否是编辑模式
    isEdit: false,
    // 编辑的商品ID
    productId: null,

    // 常量配置
    MAX_UPLOAD_COUNT: 9,
    MAX_DESCRIPTION_LENGTH: 1000,

    // 成色选项
    wearOptions: ['全新', '95新', '9成新', '8成新', '7成新', '6成新及以下'],
    wearMap: {
      '全新': '1',
      '95新': '2',
      '9成新': '3',
      '8成新': '4',
      '7成新': '5',
      '6成新及以下': '6'
    },

    // 表单数据
    formData: {
      description: '',
      fileList: [],
      price: '',
      wear: '',
      wearText: ''
    },

    // 页面状态
    publishing: false,
    formValid: false,
    hasValidContact: false,
    showWearPicker: false,
    showPriceInput: false,
    wearIndex: 0,
    lastSubmitTime: null,

    // 联系方式相关数据
    ContactType: ContactType,
    contactInfo: {
      mobile: '',
      wechatId: '',
      wechatQr: '',
      mobileVisible: false,
      wechatVisible: false,
      wechatQrVisible: false
    },
    
    // 联系方式弹窗状态
    showContactPicker: false,
    showMobileDialog: false,
    showWechatDialog: false,
    showWechatQrDialog: false,
    
    // 联系方式输入字段
    mobileField: '',
    wechatField: '',
    tempQrCodePath: ''
  },

  onLoad(options) {
    // 检查是否是编辑模式
    if (options.id) {
      this.setData({
        isEdit: true,
        productId: options.id
      });

      // 加载商品信息（包含联系方式）
      this.loadProductInfo(options.id);
    } else {
      // 新增模式才获取用户联系方式
      this.fetchUserContacts();
    }

    this.initFormValidation()
  },

  // 加载商品信息
  async loadProductInfo(id) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      const res = await api.getProductDetailForEdit(id);

      if (res.code === 200) {
        const product = res.data;

        // 使用系统信息服务处理图片列表
        let fileList = [];
        if (product.images) {
          const imageArray = product.images.split(',');
          fileList = await Promise.all(imageArray.map(async (img, index) => ({
            url: await systemInfoService.processImageUrl(img.trim()),
            filePath: img.trim(),
            name: `image_${index + 1}.jpg`
          })));
        }

        // 获取成色文本
        let wearText = '';
        for (const key in this.data.wearMap) {
          if (this.data.wearMap[key] === product.wear) {
            wearText = key;
            break;
          }
        }

        // 更新表单数据
        this.setData({
          'formData.description': product.description || '',
          'formData.price': product.price || 0,
          'formData.wear': product.wear || '',
          'formData.wearText': wearText,
          'formData.fileList': fileList
        });

        // 解析并适配联系方式信息
        this.parseContactInfo(product.contactInfo);

        // 更新表单验证状态
        this.initFormValidation();
        
        // 成功加载后隐藏loading
        wx.hideLoading();
      } else {
        throw new Error(res.msg || '加载商品信息失败');
      }
    } catch (error) {
      console.error('加载商品信息失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 解析并适配联系方式信息
  parseContactInfo(contactInfoJson) {
    if (!contactInfoJson) {
      return;
    }

    try {
      const contacts = JSON.parse(contactInfoJson);
      let contactInfo = {
        mobile: '',
        wechatId: '',
        wechatQr: '',
        mobileVisible: false,
        wechatVisible: false,
        wechatQrVisible: false
      };

      // 遍历联系方式数组，适配到页面数据结构
      contacts.forEach(contact => {
        const contactType = parseInt(contact.type);
        if (contactType === ContactType.MOBILE.code) {
          contactInfo.mobile = contact.value;
          contactInfo.mobileVisible = contact.visible;
        } else if (contactType === ContactType.WECHAT_ID.code) {
          contactInfo.wechatId = contact.value;
          contactInfo.wechatVisible = contact.visible;
        } else if (contactType === ContactType.WECHAT_QR.code) {
          if (contact.value) {
            contactInfo.wechatQr = util.formatImageUrl(contact.value);
          }
          contactInfo.wechatQrVisible = contact.visible;
        }
      });

      this.setData({
        contactInfo
      });
      // 更新表单验证状态
      this.initFormValidation();
    } catch (error) {
      console.error('解析联系方式信息失败:', error);
    }
  },

  // 初始化表单验证
  initFormValidation() {
    const {
      description,
      fileList,
      price,
      wear
    } = this.data.formData

    const { contactInfo } = this.data

    // 添加文件数量验证（文件为可选）
    const isFileValid = fileList.length === 0 || fileList.length <= this.data.MAX_UPLOAD_COUNT
    // 添加描述长度验证
    const isDescriptionValid = description.trim().length > 0 &&
      description.length <= this.data.MAX_DESCRIPTION_LENGTH
    // 添加价格验证
    const isPriceValid = parseFloat(price) > 0
    // 添加成色验证
    const isWearValid = wear !== ''
    // 添加联系方式验证 - 至少选择一种联系方式且设置为可见
    const isContactValid = (contactInfo.mobile && contactInfo.mobileVisible) ||
      (contactInfo.wechatId && contactInfo.wechatVisible) ||
      (contactInfo.wechatQr && contactInfo.wechatQrVisible)

    const formValid = isDescriptionValid && isFileValid && isPriceValid && isWearValid && isContactValid

    this.setData({
      formValid,
      hasValidContact: isContactValid
    })
  },

  // 显示价格输入弹窗
  showPriceInput() {
    this.setData({
      showPriceInput: true
    })
  },

  // 处理按键点击
  onKeyPress(e) {
    const key = e.currentTarget.dataset.key
    let price = this.data.formData.price || ''

    // 处理小数点
    if (key === '.') {
      if (price.includes('.')) return
      if (!price) price = '0'
    }

    // 处理数字
    if (key !== '.') {
      // 如果是第一个字符且为0，则替换
      if (price === '0' && !price.includes('.')) {
        price = key
      } else {
        price += key
      }
    } else {
      price += key
    }

    // 限制小数位数为2位
    const parts = price.split('.')
    if (parts.length === 2 && parts[1].length > 2) {
      return
    }

    // 限制最大值
    if (parseFloat(price) > 99999.99) {
      return
    }

    this.setData({
      'formData.price': price
    })
  },

  // 处理删除
  onDelete() {
    let price = this.data.formData.price || ''
    if (price.length > 0) {
      price = price.slice(0, -1)
    }
    this.setData({
      'formData.price': price
    })
  },

  // 确认价格
  onConfirmPrice() {
    let price = this.data.formData.price || ''

    // 验证价格范围
    const numPrice = parseFloat(price)
    if (isNaN(numPrice) || numPrice < 0.01 || numPrice > 99999.99) {
      wx.showToast({
        title: '请输入0.01-99999.99之间的价格',
        icon: 'none'
      })
      return
    }

    // 格式化价格
    if (!price.includes('.')) {
      price = price + '.00'
    } else if (price.split('.')[1].length === 1) {
      price = price + '0'
    }

    this.setData({
      'formData.price': price,
      showPriceInput: false
    })
    this.initFormValidation()
  },

  // 关闭价格输入弹窗
  onClosePriceInput() {
    this.setData({
      showPriceInput: false
    })
  },

  // 显示成色选择器
  showWearPicker() {
    // 如果有已选择的值，设置默认索引
    const index = this.data.formData.wear ?
      this.data.wearOptions.indexOf(this.data.formData.wear) : 0

    this.setData({
      showWearPicker: true,
      wearIndex: index
    })
  },

  // 关闭成色选择器
  onCloseWearPicker() {
    this.setData({
      showWearPicker: false
    })
  },

  // 选择成色
  onSelectWear(e) {
    const { value } = e.detail
    this.setData({
      'formData.wear': this.data.wearMap[value],
      'formData.wearText': value,
      showWearPicker: false
    })
    this.initFormValidation()
  },

  // 处理描述变化
  onDescriptionChange(e) {
    const description = e.detail.value || ''
    // 限制字数
    if (description.length > this.data.MAX_DESCRIPTION_LENGTH) {
      wx.showToast({
        title: `描述最多${this.data.MAX_DESCRIPTION_LENGTH}字`,
        icon: 'none'
      })
      return
    }

    this.setData({
      'formData.description': description
    })
    this.initFormValidation()
  },

  // 处理文件上传
  async afterRead(e) {
    const { file } = e.detail
    // 转换为数组处理，支持单文件和多文件
    const files = Array.isArray(file) ? file : [file]

    // 检查文件总数量限制
    if (this.data.formData.fileList.length + files.length > this.data.MAX_UPLOAD_COUNT) {
      wx.showToast({
        title: `最多上传${this.data.MAX_UPLOAD_COUNT}个文件`,
        icon: 'none'
      })
      return
    }

    // 检查图片比例并处理
    for (const fileItem of files) {
      await this.processImageWithCrop(fileItem)
    }
  },

  // 处理单张图片，检查比例并在需要时裁剪
  async processImageWithCrop(file) {
    try {
      // 获取图片信息
      const imageInfo = await this.getImageInfo(file.url)
      const aspectRatio = imageInfo.width / imageInfo.height
      
      // 更严格的比例限制，确保图片展示效果
      // 竖图：0.7-0.85 (约3:4到4:5)
      // 正方形：0.95-1.05 (接近1:1)
      // 横图：1.15-1.5 (约4:3到3:2)
      const isVerticalSuitable = aspectRatio >= 0.7 && aspectRatio <= 0.85
      const isSquareSuitable = aspectRatio >= 0.95 && aspectRatio <= 1.05
      const isHorizontalSuitable = aspectRatio >= 1.15 && aspectRatio <= 1.5
      
      if (!isVerticalSuitable && !isSquareSuitable && !isHorizontalSuitable) {
        // 比例不合适，让用户裁剪
        this.cropImage(file.url)
      } else {
        // 比例合适，直接上传
        await this.uploadSingleFile(file)
      }
    } catch (error) {
      console.error('处理图片失败:', error)
      // 如果获取图片信息失败，直接上传
      await this.uploadSingleFile(file)
    }
  },

  // 获取图片信息
  getImageInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: filePath,
        success: resolve,
        fail: reject
      })
    })
  },

  // 调用微信官方裁剪
  cropImage(filePath) {
    // 根据原图比例智能选择裁剪比例
    this.getImageInfo(filePath).then(imageInfo => {
      const aspectRatio = imageInfo.width / imageInfo.height
      
      let cropScale = '1:1' // 默认正方形
      
      if (aspectRatio > 1.5) {
        // 很宽的横图，使用4:3
        cropScale = '4:3'
      } else if (aspectRatio < 0.7) {
        // 很细的竖图，使用3:4
        cropScale = '3:4'
      }
      // 其他情况使用1:1正方形
      
      wx.cropImage({
        src: filePath,
        cropScale: cropScale,
        success: (cropRes) => {
          this.uploadCroppedImage(cropRes.tempFilePath)
        },
        fail: (cropError) => {
          console.error('裁剪失败:', cropError)
          this.uploadSingleFile({ url: filePath })
        }
      })
    }).catch(error => {
      // 获取图片信息失败，使用默认正方形裁剪
      wx.cropImage({
        src: filePath,
        cropScale: '1:1',
        success: (cropRes) => {
          this.uploadCroppedImage(cropRes.tempFilePath)
        },
        fail: (cropError) => {
          console.error('裁剪失败:', cropError)
          this.uploadSingleFile({ url: filePath })
        }
      })
    })
  },

  // 上传裁剪后的图片
  async uploadCroppedImage(croppedFilePath) {
    wx.showLoading({
      title: '上传中...'
    })

    try {
      const uploadedFile = await util.uploadFile({
        filePath: croppedFilePath,
        type: 3
      })

      if (!uploadedFile || !uploadedFile.filePath) {
        throw new Error('文件上传失败，未获取到文件路径')
      }

      // 更新文件列表
      this.setData({
        'formData.fileList': [...this.data.formData.fileList, {
          filePath: uploadedFile.filePath,
          url: croppedFilePath
        }]
      })

      this.initFormValidation()
      wx.hideLoading()
      
      wx.showToast({
        title: '图片已上传',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('上传裁剪图片失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    }
  },

  // 上传单张图片
  async uploadSingleFile(file) {
    wx.showLoading({
      title: '上传中...'
    })

    try {
      const uploadedFile = await util.uploadFile({
        filePath: file.url,
        type: 3
      })

      if (!uploadedFile || !uploadedFile.filePath) {
        throw new Error('文件上传失败，未获取到文件路径')
      }

      // 更新文件列表
      this.setData({
        'formData.fileList': [...this.data.formData.fileList, {
          filePath: uploadedFile.filePath,
          url: file.url
        }]
      })

      this.initFormValidation()
      wx.hideLoading()
      
      wx.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('上传文件失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    }
  },

  // 删除文件
  onDeleteFile(e) {
    const { index } = e.detail
    if (index < 0 || index >= this.data.formData.fileList.length) return

    const fileList = this.data.formData.fileList.filter((_, idx) => idx !== index)

    this.setData({
      'formData.fileList': fileList
    })
    this.initFormValidation()
  },

  // 获取用户联系方式
  fetchUserContacts() {
    api.getUserContacts().then(res => {
      if (res.code === 200) {
        const contacts = res.data || [];
        let contactInfo = {
          mobile: '',
          wechatId: '',
          wechatQr: '',
          mobileVisible: false,
          wechatVisible: false,
          wechatQrVisible: false
        };

        // 处理联系方式数据
        contacts.forEach(contact => {
          const contactType = parseInt(contact.contactType);
          if (contactType === ContactType.MOBILE.code) {
            contactInfo.mobile = contact.contactValue;
            contactInfo.mobileVisible = contact.isVisible;
          } else if (contactType === ContactType.WECHAT_ID.code) {
            contactInfo.wechatId = contact.contactValue;
            contactInfo.wechatVisible = contact.isVisible;
          } else if (contactType === ContactType.WECHAT_QR.code) {
            if (contact.contactValue) {
              contactInfo.wechatQr = util.formatImageUrl(contact.contactValue);
            }
            contactInfo.wechatQrVisible = contact.isVisible;
          }
        });

        this.setData({
          contactInfo
        });
        // 更新表单验证状态
        this.initFormValidation();
      }
    }).catch(err => {
      console.error('获取联系方式失败:', err);
    });
  },

  // 显示联系方式选择器
  showContactPicker() {
    this.setData({
      showContactPicker: true
    });
  },

  // 关闭联系方式选择器
  onCloseContactPicker() {
    this.setData({
      showContactPicker: false
    });
  },

  // 阻止事件冒泡
  preventTap() {
    // 阻止事件冒泡，防止点击开关时同时触发整个item的点击事件
  },

  // 显示手机号编辑弹窗
  showMobileDialog() {
    this.setData({
      showMobileDialog: true,
      mobileField: this.data.contactInfo.mobile || '',
      showContactPicker: false
    });
  },

  // 关闭手机号编辑弹窗
  onCloseMobileDialog() {
    this.setData({
      showMobileDialog: false,
      mobileField: ''
    });
  },

  // 验证手机号
  validateMobile(mobile) {
    const regex = /^1[3-9]\d{9}$/;
    if (!mobile) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    if (!regex.test(mobile)) {
      wx.showToast({
        title: '请输入有效的11位手机号码',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    return true;
  },

  // 确认修改手机号
  onConfirmMobile() {
    if (!this.validateMobile(this.data.mobileField)) {
      return;
    }

    // 只更新本地数据，不调用接口，不显示提示，默认设置为可见
    this.setData({
      showMobileDialog: false,
      'contactInfo.mobile': this.data.mobileField,
      'contactInfo.mobileVisible': true
    });
    // 更新表单验证状态
    this.initFormValidation();
  },

  // 手机号可见性变更
  onMobileVisibilityChange(e) {
    const isVisible = e.detail;

    // 如果要设置为不可见，检查是否至少还有一个联系方式可见
    if (!isVisible) {
      const { contactInfo } = this.data;
      const hasOtherVisible = (contactInfo.wechatId && contactInfo.wechatVisible) ||
        (contactInfo.wechatQr && contactInfo.wechatQrVisible);

      if (!hasOtherVisible) {
        wx.showToast({
          title: '至少需要一种联系方式',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }

    // 只更新本地数据，不调用接口，不显示提示
    this.setData({
      'contactInfo.mobileVisible': isVisible
    });
    // 更新表单验证状态
    this.initFormValidation();
  },

  // 显示微信号编辑弹窗
  showWechatDialog() {
    this.setData({
      showWechatDialog: true,
      wechatField: this.data.contactInfo.wechatId || '',
      showContactPicker: false
    });
  },

  // 关闭微信号编辑弹窗
  onCloseWechatDialog() {
    this.setData({
      showWechatDialog: false,
      wechatField: ''
    });
  },

  // 验证微信号
  validateWechatId(wechatId) {
    const regex = /^[a-zA-Z0-9_-]{6,20}$/;
    if (!wechatId) {
      wx.showToast({
        title: '微信号不能为空',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    if (!regex.test(wechatId)) {
      wx.showToast({
        title: '微信号格式不正确',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    return true;
  },

  // 确认修改微信号
  onConfirmWechat() {
    if (!this.validateWechatId(this.data.wechatField)) {
      return;
    }

    // 只更新本地数据，不调用接口，不显示提示，默认设置为可见
    this.setData({
      showWechatDialog: false,
      'contactInfo.wechatId': this.data.wechatField,
      'contactInfo.wechatVisible': true
    });
    // 更新表单验证状态
    this.initFormValidation();
  },

  // 微信号可见性变更
  onWechatVisibilityChange(e) {
    const isVisible = e.detail;

    // 如果要设置为不可见，检查是否至少还有一个联系方式可见
    if (!isVisible) {
      const { contactInfo } = this.data;
      const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
        (contactInfo.wechatQr && contactInfo.wechatQrVisible);

      if (!hasOtherVisible) {
        wx.showToast({
          title: '至少需要一种联系方式',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }

    // 只更新本地数据，不调用接口，不显示提示
    this.setData({
      'contactInfo.wechatVisible': isVisible
    });
    // 更新表单验证状态
    this.initFormValidation();
  },

  // 显示微信二维码编辑弹窗
  async showWechatQrDialog() {
    const qrPath = this.data.contactInfo.wechatQr ? 
      await systemInfoService.processImageUrl(this.data.contactInfo.wechatQr) : '';

    this.setData({
      showWechatQrDialog: true,
      tempQrCodePath: qrPath,
      showContactPicker: false
    });
  },

  // 关闭微信二维码编辑弹窗
  onCloseWechatQrDialog() {
    this.setData({
      showWechatQrDialog: false,
      tempQrCodePath: ''
    });
  },

  // 选择微信二维码图片
  chooseQrCode() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.setData({
          tempQrCodePath: tempFilePath
        });
      }
    });
  },

  // 确认上传微信二维码
  onConfirmWechatQr() {
    if (!this.data.tempQrCodePath) {
      wx.showToast({
        title: '请选择二维码图片',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '上传中...' });

    // 使用通用上传方法上传图片
    util.uploadFile({
      filePath: this.data.tempQrCodePath,
      type: 6 // 微信二维码类型
    }).then(uploadResult => {
      // 上传成功后，保存上传后的文件路径，默认设置为可见
      this.setData({
        showWechatQrDialog: false,
        'contactInfo.wechatQr': uploadResult.filePath,
        'contactInfo.wechatQrVisible': true,
        tempQrCodePath: ''
      });

      // 更新表单验证状态
      this.initFormValidation();

      wx.hideLoading();
      wx.showToast({
        title: '二维码上传成功',
        icon: 'success'
      });
    }).catch(err => {
      console.error('上传微信二维码失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '上传失败，请重试',
        icon: 'none'
      });
    });
  },

  // 微信二维码可见性变更
  onWechatQrVisibilityChange(e) {
    const isVisible = e.detail;

    // 如果要设置为不可见，检查是否至少还有一个联系方式可见
    if (!isVisible) {
      const { contactInfo } = this.data;
      const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
        (contactInfo.wechatId && contactInfo.wechatVisible);

      if (!hasOtherVisible) {
        wx.showToast({
          title: '至少需要一种联系方式',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }

    // 只更新本地数据，不调用接口，不显示提示
    this.setData({
      'contactInfo.wechatQrVisible': isVisible
    });
    // 更新表单验证状态
    this.initFormValidation();
  },

  // 处理表单提交
  async handleSubmit(e) {
    // 防重复提交检查 - 多重保护
    if (this.data.publishing) {
      return;
    }

    // 防抖检查 - 1秒内不允许重复点击
    const now = Date.now();
    if (this.data.lastSubmitTime && (now - this.data.lastSubmitTime) < 1000) {
      wx.showToast({
        title: '请勿频繁点击',
        icon: 'none',
        duration: 1000
      });
      return;
    }
    this.data.lastSubmitTime = now;

    const formData = e.detail.value;

    // 表单验证
    if (!this.data.formData.fileList.length) {
      wx.showToast({
        title: '请至少上传一张图片',
        icon: 'none'
      });
      return;
    }
    if (!formData.description.trim()) {
      wx.showToast({
        title: '请描述您的商品',
        icon: 'none'
      });
      return;
    }
    if (parseFloat(this.data.formData.price) <= 0) {
      wx.showToast({
        title: '请输入有效的价格',
        icon: 'none'
      });
      return;
    }
    if (!this.data.formData.wear) {
      wx.showToast({
        title: '请选择商品成色',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({
        publishing: true
      })

      wx.showLoading({
        title: this.data.isEdit ? '更新中...' : '发布中...'
      })

      // 构建联系方式JSON数据
      const contactData = [];

      // 添加手机号联系方式
      if (this.data.contactInfo.mobile) {
        contactData.push({
          type: ContactType.MOBILE.code,
          value: this.data.contactInfo.mobile,
          visible: this.data.contactInfo.mobileVisible
        });
      }

      // 添加微信号联系方式
      if (this.data.contactInfo.wechatId) {
        contactData.push({
          type: ContactType.WECHAT_ID.code,
          value: this.data.contactInfo.wechatId,
          visible: this.data.contactInfo.wechatVisible
        });
      }

      // 添加微信二维码联系方式
      if (this.data.contactInfo.wechatQr) {
        contactData.push({
          type: ContactType.WECHAT_QR.code,
          value: this.data.contactInfo.wechatQr,
          visible: this.data.contactInfo.wechatQrVisible
        });
      }

      let res;
      const productData = {
        description: formData.description.trim(),
        price: this.data.formData.price,
        wear: this.data.formData.wear,
        images: this.data.formData.fileList.map(f => f.filePath).join(','),
        contactInfo: JSON.stringify(contactData)
      };

      if (this.data.isEdit) {
        // 编辑模式，更新商品
        productData.id = this.data.productId;
        res = await api.updateProduct(productData);
      } else {
        // 新增模式，添加商品
        res = await api.addProduct(productData);
      }

      wx.hideLoading()

      if (res.code === 200) {
        wx.showToast({
          title: this.data.isEdit ? '更新成功' : '发布成功',
          success: () => {
            setTimeout(() => {
              // 获取商品ID
              let productId;
              if (this.data.isEdit) {
                // 编辑模式，使用当前商品ID
                productId = this.data.productId;
              } else {
                // 新增模式，使用返回的商品ID
                productId = res.data;
              }

              // 跳转到商品详情页面
              wx.redirectTo({
                url: `/pages/item/detail?id=${productId}`,
                fail: () => {
                  // 如果跳转失败，则返回上一页
                  wx.navigateBack()
                }
              })
            }, 500)
          }
        })
      } else {
        throw new Error(res.msg || (this.data.isEdit ? '更新失败' : '发布失败'))
      }
    } catch (error) {
      console.error(this.data.isEdit ? '更新失败:' : '发布失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: error.message || (this.data.isEdit ? '更新失败' : '发布失败'),
        icon: 'none'
      })
    } finally {
      this.setData({
        publishing: false
      })
    }
  }
});