const util = require("../../utils/util")
const api = require("../../config/api")
const ContactType = require('../../utils/ContactType.js')
const systemInfoService = require('../../services/systemInfo.js')

Component({
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 联系方式数据
    contactInfo: {
      type: Object,
      value: {
        mobile: '',
        wechatId: '',
        wechatQr: '',
        mobileVisible: false,
        wechatVisible: false,
        wechatQrVisible: false
      }
    }
  },

  data: {
    // 弹窗状态
    showMobileDialog: false,
    showWechatDialog: false,
    showWechatQrDialog: false,
    
    // 输入字段
    mobileField: '',
    wechatField: '',
    tempQrCodePath: ''
  },

  methods: {
    // 关闭主弹窗
    onClose() {
      this.triggerEvent('close')
    },

    // 阻止事件冒泡
    preventTap() {
      // 阻止事件冒泡，防止点击开关时同时触发整个item的点击事件
    },

    // 显示手机号编辑弹窗
    showMobileDialog() {
      this.setData({
        showMobileDialog: true,
        mobileField: this.properties.contactInfo.mobile || ''
      })
    },

    // 关闭手机号编辑弹窗
    onCloseMobileDialog() {
      this.setData({
        showMobileDialog: false,
        mobileField: ''
      })
    },

    // 验证手机号
    validateMobile(mobile) {
      const regex = /^1[3-9]\d{9}$/
      if (!mobile) {
        wx.showToast({
          title: '手机号不能为空',
          icon: 'none',
          duration: 2000
        })
        return false
      }
      if (!regex.test(mobile)) {
        wx.showToast({
          title: '请输入有效的11位手机号码',
          icon: 'none',
          duration: 2000
        })
        return false
      }
      return true
    },

    // 确认修改手机号
    onConfirmMobile() {
      if (!this.validateMobile(this.data.mobileField)) {
        return
      }

      const newContactInfo = {
        ...this.properties.contactInfo,
        mobile: this.data.mobileField,
        mobileVisible: true
      }

      this.setData({
        showMobileDialog: false
      })

      this.triggerEvent('contactChange', {
        contactInfo: newContactInfo,
        type: 'mobile'
      })
    },

    // 手机号可见性变更
    onMobileVisibilityChange(e) {
      const isVisible = e.detail
      const { contactInfo } = this.properties

      // 如果要设置为不可见，检查是否至少还有一个联系方式可见
      if (!isVisible) {
        const hasOtherVisible = (contactInfo.wechatId && contactInfo.wechatVisible) ||
          (contactInfo.wechatQr && contactInfo.wechatQrVisible)

        if (!hasOtherVisible) {
          wx.showToast({
            title: '至少需要一种联系方式',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      const newContactInfo = {
        ...contactInfo,
        mobileVisible: isVisible
      }

      this.triggerEvent('contactChange', {
        contactInfo: newContactInfo,
        type: 'mobileVisibility'
      })
    },

    // 显示微信号编辑弹窗
    showWechatDialog() {
      this.setData({
        showWechatDialog: true,
        wechatField: this.properties.contactInfo.wechatId || ''
      })
    },

    // 关闭微信号编辑弹窗
    onCloseWechatDialog() {
      this.setData({
        showWechatDialog: false,
        wechatField: ''
      })
    },

    // 验证微信号
    validateWechatId(wechatId) {
      const regex = /^[a-zA-Z0-9_-]{6,20}$/
      if (!wechatId) {
        wx.showToast({
          title: '微信号不能为空',
          icon: 'none',
          duration: 2000
        })
        return false
      }
      if (!regex.test(wechatId)) {
        wx.showToast({
          title: '微信号格式不正确',
          icon: 'none',
          duration: 2000
        })
        return false
      }
      return true
    },

    // 确认修改微信号
    onConfirmWechat() {
      if (!this.validateWechatId(this.data.wechatField)) {
        return
      }

      const newContactInfo = {
        ...this.properties.contactInfo,
        wechatId: this.data.wechatField,
        wechatVisible: true
      }

      this.setData({
        showWechatDialog: false
      })

      this.triggerEvent('contactChange', {
        contactInfo: newContactInfo,
        type: 'wechat'
      })
    },

    // 微信号可见性变更
    onWechatVisibilityChange(e) {
      const isVisible = e.detail
      const { contactInfo } = this.properties

      // 如果要设置为不可见，检查是否至少还有一个联系方式可见
      if (!isVisible) {
        const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
          (contactInfo.wechatQr && contactInfo.wechatQrVisible)

        if (!hasOtherVisible) {
          wx.showToast({
            title: '至少需要一种联系方式',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      const newContactInfo = {
        ...contactInfo,
        wechatVisible: isVisible
      }

      this.triggerEvent('contactChange', {
        contactInfo: newContactInfo,
        type: 'wechatVisibility'
      })
    },

    // 显示微信二维码编辑弹窗
    async showWechatQrDialog() {
      const qrPath = this.properties.contactInfo.wechatQr ? 
        await systemInfoService.processImageUrl(this.properties.contactInfo.wechatQr) : ''

      this.setData({
        showWechatQrDialog: true,
        tempQrCodePath: qrPath
      })
    },

    // 关闭微信二维码编辑弹窗
    onCloseWechatQrDialog() {
      this.setData({
        showWechatQrDialog: false,
        tempQrCodePath: ''
      })
    },

    // 选择微信二维码图片
    chooseQrCode() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath
          this.setData({
            tempQrCodePath: tempFilePath
          })
        }
      })
    },

    // 确认上传微信二维码
    onConfirmWechatQr() {
      if (!this.data.tempQrCodePath) {
        wx.showToast({
          title: '请选择二维码图片',
          icon: 'none'
        })
        return
      }

      wx.showLoading({ title: '上传中...' })

      // 使用通用上传方法上传图片
      util.uploadFile({
        filePath: this.data.tempQrCodePath,
        type: 6 // 微信二维码类型
      }).then(uploadResult => {
        const newContactInfo = {
          ...this.properties.contactInfo,
          wechatQr: uploadResult.filePath,
          wechatQrVisible: true
        }

        this.setData({
          showWechatQrDialog: false,
          tempQrCodePath: ''
        })

        wx.hideLoading()
        wx.showToast({
          title: '二维码上传成功',
          icon: 'success'
        })

        this.triggerEvent('contactChange', {
          contactInfo: newContactInfo,
          type: 'wechatQr'
        })
      }).catch(err => {
        console.error('上传微信二维码失败:', err)
        wx.hideLoading()
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        })
      })
    },

    // 微信二维码可见性变更
    onWechatQrVisibilityChange(e) {
      const isVisible = e.detail
      const { contactInfo } = this.properties

      // 如果要设置为不可见，检查是否至少还有一个联系方式可见
      if (!isVisible) {
        const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
          (contactInfo.wechatId && contactInfo.wechatVisible)

        if (!hasOtherVisible) {
          wx.showToast({
            title: '至少需要一种联系方式',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      const newContactInfo = {
        ...contactInfo,
        wechatQrVisible: isVisible
      }

      this.triggerEvent('contactChange', {
        contactInfo: newContactInfo,
        type: 'wechatQrVisibility'
      })
    }
  }
})
