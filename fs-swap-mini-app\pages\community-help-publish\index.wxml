<!--pages/community-help-publish/index.wxml-->
<view class="container">
  <form bindsubmit="onSubmit">
    <!-- 内容卡片 -->
    <view class="content-card">
      <!-- 标题输入区域 -->
      <view class="title-area">
        <input
          name="title"
          placeholder="请输入标题..."
          value="{{ formData.title }}"
          bindinput="onTitleChange"
          maxlength="50"
          class="title-input"
        />
        <view class="title-count">{{ titleLength }}/50</view>
      </view>
      <view class="divider"></view>
      <view class="description-area">
        <textarea
          name="content"
          placeholder="描述您的互助需求或提供的帮助..."
          value="{{ formData.content }}"
          bindinput="onContentChange"
          maxlength="500"
        />
        <view class="word-count">{{ formData.content.length }}/500</view>
      </view>
      <view class="upload-section">
        <van-uploader
          file-list="{{ fileList }}"
          deletable="{{ true }}"
          bind:after-read="onImageUpload"
          bind:delete="onImageDelete"
          max-count="6"
          multiple="{{ true }}"
          upload-text="添加图片"
          image-fit="aspectFill"
        />
      </view>
    </view>

    <!-- 分类和联系方式卡片 -->
    <view class="trade-card">
      <view class="condition-section" bindtap="onCategorySelect">
        <text class="condition-label">分类</text>
        <view class="condition-value">
          <text>{{ selectedCategory.dictLabel || '请选择分类' }}</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="condition-section" bindtap="onEndTimeSelect">
        <text class="condition-label">截止日期</text>
        <view class="condition-value">
          <text>{{ endTimeDisplay || '请选择截止日期' }}</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="condition-section" bindtap="showContactPicker">
        <text class="condition-label">联系方式</text>
        <view class="condition-value">
          <view class="contact-icons">
            <van-icon name="phone" size="20px" color="{{ contactInfo.mobileVisible ? '#4080FF' : '#cccccc' }}" />
            <van-icon name="wechat" size="20px" color="{{ contactInfo.wechatVisible ? '#4080FF' : '#cccccc' }}" />
            <van-icon name="qr-invalid" size="20px" color="{{ contactInfo.wechatQrVisible ? '#4080FF' : '#cccccc' }}" />
          </view>
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <!-- 费用提示 -->
    <view wx:if="{{ !isEdit }}" class="fee-notice">
      <van-icon name="info-o" size="16px" color="#ff6b6b" />
      <text class="fee-text">发布互助信息需要消耗 10 碳豆</text>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        form-type="submit"
        class="publish-btn"
        loading="{{ submitLoading }}"
        disabled="{{ !canSubmit }}"
      >{{ isEdit ? '更新' : '发布' }}</button>
    </view>
  </form>

  <!-- 分类选择器 -->
  <van-popup
    show="{{ showCategoryPicker }}"
    position="bottom"
    bind:close="onCategoryPickerClose"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <van-picker
      columns="{{ categoryColumns }}"
      value-key="text"
      bind:confirm="onCategoryConfirm"
      bind:cancel="onCategoryPickerClose"
      confirm-button-text="确认"
      cancel-button-text="取消"
      show-toolbar="{{ true }}"
      title="选择分类"
    />
  </van-popup>



  <!-- 截止时间选择器 -->
  <van-popup
    show="{{ showEndTimePicker }}"
    position="bottom"
    bind:close="onEndTimePickerClose"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <van-datetime-picker
      type="date"
      value="{{ endTimeValue }}"
      bind:confirm="onEndTimeConfirm"
      bind:cancel="onEndTimePickerClose"
      confirm-button-text="确认"
      cancel-button-text="取消"
      show-toolbar="{{ true }}"
      title="选择截止日期"
      min-date="{{ minDate }}"
    />
  </van-popup>

  <!-- 联系方式管理组件 -->
  <contact-manager
    show="{{ showContactPicker }}"
    contactInfo="{{ contactInfo }}"
    bind:close="onCloseContactPicker"
    bind:contactChange="onContactChange"
  />

  <!-- 发布确认弹窗 -->
  <van-dialog
    use-slot
    title="{{ isEdit ? '确认更新' : '确认发布' }}"
    show="{{ showPublishDialog }}"
    show-cancel-button
    confirm-button-text="{{ isEdit ? '确认更新' : '确认发布' }}"
    cancel-button-text="取消"
    bind:close="onPublishDialogClose"
    bind:confirm="onPublishConfirm"
  >
    <view class="publish-dialog-content">
      <text wx:if="{{ !isEdit }}">发布互助信息需要消耗 10 碳豆，确认发布吗？</text>
      <text wx:else>确认更新互助信息吗？</text>
    </view>
  </van-dialog>
</view>
