const api = require('../../config/api.js')
const systemInfoService = require('../../services/systemInfo')
const ContactType = require('../../utils/ContactType.js')
const util = require('../../utils/util')
const dateUtil = require('../../utils/dateUtil')

Page({
  data: {
    // 常量配置
    MAX_UPLOAD_COUNT: 6,
    MAX_CONTENT_LENGTH: 500,

    // 表单数据
    formData: {
      title: '',
      content: '',
      category: '',
      publishType: '',
      endTime: ''
    },

    // 图片列表
    fileList: [],

    // 分类相关
    categories: [],
    selectedCategory: {},
    showCategoryPicker: false,
    categoryColumns: [],

    // 发布类型相关
    publishTypes: [],
    selectedPublishType: {},
    showPublishTypePicker: false,
    publishTypeColumns: [],

    // 联系方式相关数据
    ContactType: ContactType,
    contactInfo: {
      mobile: '',
      wechatId: '',
      wechatQr: '',
      mobileVisible: false,
      wechatVisible: false,
      wechatQrVisible: false
    },

    // 联系方式弹窗状态
    showContactPicker: false,
    showMobileDialog: false,
    showWechatDialog: false,
    showWechatQrDialog: false,

    // 联系方式输入字段
    mobileField: '',
    wechatField: '',
    tempQrCodePath: '',

    // 提交状态
    submitLoading: false,
    canSubmit: false,

    // 发布确认弹窗
    showPublishDialog: false,

    // 截止时间相关
    showEndTimePicker: false,
    endTimeValue: new Date().getTime(),
    endTimeDisplay: '',
    minDate: new Date().getTime(),

    // 预设发布类型
    presetPublishType: '',

    // 编辑模式相关
    isEdit: false,
    helpId: null,

    // 计算属性
    titleLength: 0
  },

  onLoad(options) {
    console.log('发布互助页面加载', options)

    // 检查是否是编辑模式
    if (options && options.id && options.mode === 'edit') {
      this.setData({
        isEdit: true,
        helpId: options.id
      })

      wx.setNavigationBarTitle({
        title: '编辑互助'
      })

      // 编辑模式：先加载分类和发布类型，再加载互助详情
      this.loadCategories().then(() => {
        this.loadPublishTypes().then(() => {
          this.loadHelpDetail(options.id)
        })
      })
    } else {
      // 新增模式
      // 如果有预设的发布类型，设置到表单数据中
      if (options && options.publishType) {
        // 保持字符串类型
        const publishTypeStr = options.publishType.toString()

        this.setData({
          'formData.publishType': publishTypeStr,
          presetPublishType: publishTypeStr
        })

        // 根据发布类型设置页面标题
        let title = '发布互助'
        if (publishTypeStr === '1') {
          title = '发布需求'
        } else if (publishTypeStr === '2') {
          title = '提供服务'
        }

        wx.setNavigationBarTitle({
          title: title
        })
      }

      this.loadCategories()
      this.loadPublishTypes()
      this.fetchUserContacts()
    }

    this.initFormValidation()
  },

  /**
   * 加载互助详情（编辑模式）
   */
  async loadHelpDetail(helpId) {
    try {
      wx.showLoading({ title: '加载中...' })

      const response = await api.getCommunityHelpDetail(helpId)
      const helpDetail = response.data

      wx.hideLoading()

      // 处理图片数据
      let fileList = []
      if (helpDetail.images) {
        const images = helpDetail.images.split(',').filter(img => img.trim())
        fileList = images.map((img, index) => {
          // 如果图片URL不是完整URL，添加文件服务器前缀
          let imageUrl = img
          if (!img.startsWith('http')) {
            const systemInfo = wx.getStorageSync('systemInfo')
            imageUrl = systemInfo?.fileUrl ? systemInfo.fileUrl + img : img
          }
          return {
            url: imageUrl,
            filePath: img, // 保存原始路径用于提交
            isImage: true
          }
        })
      }

      // 处理联系方式
      let contactInfo = {
        mobile: '',
        wechatId: '',
        wechatQr: '',
        mobileVisible: false,
        wechatVisible: false,
        wechatQrVisible: false
      }

      if (helpDetail.contactInfo) {
        try {
          const contacts = JSON.parse(helpDetail.contactInfo)
          // 遍历联系方式数组，适配到页面数据结构
          contacts.forEach(contact => {
            const contactType = parseInt(contact.type)
            if (contactType === ContactType.MOBILE.code) {
              contactInfo.mobile = contact.value
              contactInfo.mobileVisible = contact.visible
            } else if (contactType === ContactType.WECHAT_ID.code) {
              contactInfo.wechatId = contact.value
              contactInfo.wechatVisible = contact.visible
            } else if (contactType === ContactType.WECHAT_QR.code) {
              if (contact.value) {
                contactInfo.wechatQr = util.formatImageUrl(contact.value)
              }
              contactInfo.wechatQrVisible = contact.visible
            }
          })
        } catch (error) {
          console.error('解析联系方式失败:', error)
        }
      }

      // 处理截止时间显示
      const endTimeDisplay = dateUtil.formatToDateOnly(helpDetail.endTime)

      // 设置表单数据
      this.setData({
        'formData.title': helpDetail.title || '',
        'formData.content': helpDetail.content || '',
        'formData.category': helpDetail.category || '',
        'formData.publishType': helpDetail.publishType ? helpDetail.publishType.toString() : '',
        'formData.endTime': helpDetail.endTime || '',
        fileList: fileList,
        contactInfo: contactInfo,
        titleLength: (helpDetail.title || '').length,
        endTimeDisplay: endTimeDisplay
      })

      // 设置选中的分类
      if (helpDetail.category && this.data.categories.length > 0) {
        const selectedCategory = this.data.categories.find(cat => cat.dictValue === helpDetail.category)
        if (selectedCategory) {
          this.setData({
            selectedCategory: selectedCategory
          })
        }
      }

      // 设置选中的发布类型
      if (helpDetail.publishType && this.data.publishTypes.length > 0) {
        const selectedPublishType = this.data.publishTypes.find(type => type.dictValue === helpDetail.publishType.toString())
        if (selectedPublishType) {
          this.setData({
            selectedPublishType: selectedPublishType
          })
        }
      }

      // 重新验证表单
      this.checkCanSubmit()

    } catch (error) {
      wx.hideLoading()
      console.error('加载互助详情失败:', error)
      wx.showModal({
        title: '加载失败',
        content: '无法加载互助详情，请稍后重试',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
    }
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      // 使用统一的系统信息服务获取分类数据
      const categories = await systemInfoService.getCommunityHelpCategories()

      if (categories && categories.length > 0) {
        // 为Picker组件准备数据格式
        const categoryColumns = categories.map(item => ({
          text: item.dictLabel,
          value: item.dictValue
        }))

        this.setData({
          categories: categories,
          categoryColumns: categoryColumns
        })
      } else {
        console.warn('系统接口未返回分类数据')
        this.setData({
          categories: [],
          categoryColumns: []
        })
      }
      return Promise.resolve()
    } catch (error) {
      console.error('加载分类失败:', error)
      this.setData({
        categories: [],
        categoryColumns: []
      })
      return Promise.resolve()
    }
  },

  /**
   * 加载发布类型列表
   */
  async loadPublishTypes() {
    try {
      // 使用统一的系统信息服务获取发布类型数据
      const publishTypes = await systemInfoService.getCommunityHelpPublishTypes()

      if (publishTypes && publishTypes.length > 0) {
        this.setData({
          publishTypes: publishTypes
        })

        // 如果有预设的发布类型，设置选中的发布类型
        if (this.data.presetPublishType) {
          // 直接使用字符串进行比较
          const selectedPublishType = publishTypes.find(item => item.dictValue === this.data.presetPublishType)
          if (selectedPublishType) {
            this.setData({
              selectedPublishType: selectedPublishType
            })
          }
        }
      } else {
        console.warn('系统接口未返回发布类型数据')
        this.setData({
          publishTypes: []
        })
      }
      return Promise.resolve()
    } catch (error) {
      console.error('加载发布类型失败:', error)
      this.setData({
        publishTypes: []
      })
      return Promise.resolve()
    }
  },

  /**
   * 标题输入
   */
  onTitleChange(event) {
    const title = event.detail.value || ''
    this.setData({
      'formData.title': title,
      titleLength: title.length
    })
    this.checkCanSubmit()
  },

  /**
   * 内容输入
   */
  onContentChange(event) {
    this.setData({
      'formData.content': event.detail.value
    })
    this.checkCanSubmit()
  },

  /**
   * 获取用户联系方式
   */
  async fetchUserContacts() {
    try {
      const res = await api.getUserContacts()
      if (res.code === 200 && res.data) {
        const contacts = res.data
        let contactInfo = {
          mobile: '',
          wechatId: '',
          wechatQr: '',
          mobileVisible: false,
          wechatVisible: false,
          wechatQrVisible: false
        }

        // 处理联系方式数据
        contacts.forEach(contact => {
          const contactType = parseInt(contact.contactType)
          if (contactType === ContactType.MOBILE.code) {
            contactInfo.mobile = contact.contactValue
            contactInfo.mobileVisible = contact.isVisible
          } else if (contactType === ContactType.WECHAT_ID.code) {
            contactInfo.wechatId = contact.contactValue
            contactInfo.wechatVisible = contact.isVisible
          } else if (contactType === ContactType.WECHAT_QR.code) {
            if (contact.contactValue) {
              contactInfo.wechatQr = util.formatImageUrl(contact.contactValue)
            }
            contactInfo.wechatQrVisible = contact.isVisible
          }
        })

        this.setData({
          contactInfo
        })
      }
    } catch (err) {
      console.error('获取联系方式失败:', err)
    }
  },

  /**
   * 显示联系方式选择器
   */
  showContactPicker() {
    this.setData({
      showContactPicker: true
    })
  },

  /**
   * 关闭联系方式选择器
   */
  onCloseContactPicker() {
    this.setData({
      showContactPicker: false
    })
  },

  /**
   * 初始化表单验证
   */
  initFormValidation() {
    this.checkCanSubmit()
  },

  /**
   * 分类选择
   */
  onCategorySelect() {
    this.setData({
      showCategoryPicker: true
    })
  },



  /**
   * 分类选择确认
   */
  onCategoryConfirm(event) {
    const { value, index } = event.detail
    // 从categoryColumns中获取选中的值
    const selectedValue = this.data.categoryColumns[index].value
    const selectedCategory = this.data.categories.find(item => item.dictValue === selectedValue)

    this.setData({
      'formData.category': selectedValue,
      selectedCategory,
      showCategoryPicker: false
    })
    this.checkCanSubmit()
  },

  /**
   * 关闭分类选择器
   */
  onCategoryPickerClose() {
    this.setData({
      showCategoryPicker: false
    })
  },



  /**
   * 截止时间选择
   */
  onEndTimeSelect() {
    this.setData({
      showEndTimePicker: true
    })
  },

  /**
   * 截止时间确认
   */
  onEndTimeConfirm(event) {
    // 参考活动发布页面的实现，直接使用 event.detail
    const timestamp = event.detail

    try {
      // 验证日期是否有效
      if (!dateUtil.isValidDate(timestamp)) {
        wx.showToast({
          title: '时间格式错误',
          icon: 'error'
        })
        return
      }

      // 格式化显示时间 (YYYY-MM-DD)，只显示到天
      const endTimeDisplay = dateUtil.formatToDateOnly(timestamp)

      // 设置为当天的23:59:59作为截止时间
      const endTimeValue = dateUtil.setToEndOfDay(timestamp)

      this.setData({
        'formData.endTime': endTimeValue,
        endTimeValue: timestamp,
        endTimeDisplay: endTimeDisplay,
        showEndTimePicker: false
      })
      this.checkCanSubmit()
    } catch (error) {
      console.error('时间处理错误:', error)
      wx.showToast({
        title: '时间选择失败',
        icon: 'error'
      })
    }
  },

  /**
   * 关闭截止时间选择器
   */
  onEndTimePickerClose() {
    this.setData({
      showEndTimePicker: false
    })
  },

  /**
   * 图片上传
   */
  async onImageUpload(event) {
    const { file } = event.detail
    // 转换为数组处理，支持单文件和多文件
    const files = Array.isArray(file) ? file : [file]

    // 检查文件总数量限制
    if (this.data.fileList.length + files.length > this.data.MAX_UPLOAD_COUNT) {
      wx.showToast({
        title: `最多上传${this.data.MAX_UPLOAD_COUNT}个文件`,
        icon: 'none'
      })
      return
    }

    wx.showLoading({ title: '上传中...' })

    try {
      // 使用 Promise.all 同时上传多个文件
      const uploadTasks = files.map(async (file) => {
        const uploadedFile = await util.uploadFile({
          filePath: file.url,
          type: '7' // 使用类型7表示互助图片
        })

        if (!uploadedFile || !uploadedFile.filePath) {
          throw new Error('文件上传失败，未获取到文件路径')
        }

        return {
          url: file.url,
          name: '互助图片',
          isImage: true,
          filePath: uploadedFile.filePath
        }
      })

      const uploadedFiles = await Promise.all(uploadTasks)

      // 更新文件列表，添加所有上传成功的文件
      this.setData({
        fileList: [...this.data.fileList, ...uploadedFiles]
      })

      wx.hideLoading()
      wx.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1000
      })
    } catch (error) {
      wx.hideLoading()
      console.error('图片上传失败:', error)
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    }
  },

  /**
   * 删除图片
   */
  onImageDelete(event) {
    const { index } = event.detail
    const fileList = this.data.fileList
    fileList.splice(index, 1)
    this.setData({ fileList })
  },

  /**
   * 显示手机号编辑弹窗
   */
  showMobileDialog() {
    this.setData({
      showMobileDialog: true,
      mobileField: this.data.contactInfo.mobile || '',
      showContactPicker: false
    })
  },

  /**
   * 关闭手机号编辑弹窗
   */
  onCloseMobileDialog() {
    this.setData({
      showMobileDialog: false,
      mobileField: ''
    })
  },

  /**
   * 手机号输入变化
   */
  onMobileFieldChange(e) {
    this.setData({
      mobileField: e.detail
    })
  },

  /**
   * 确认手机号
   */
  onConfirmMobile() {
    const mobile = this.data.mobileField.trim()
    if (!mobile) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    if (!/^1[3-9]\d{9}$/.test(mobile)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    this.setData({
      'contactInfo.mobile': mobile,
      'contactInfo.mobileVisible': true,
      showMobileDialog: false,
      mobileField: ''
    })
    this.initFormValidation()
  },

  /**
   * 手机号可见性变更
   */
  onMobileVisibilityChange(e) {
    const isVisible = e.detail

    // 如果要设置为不可见，检查是否至少还有一个联系方式可见
    if (!isVisible) {
      const { contactInfo } = this.data
      const hasOtherVisible = (contactInfo.wechatId && contactInfo.wechatVisible) ||
        (contactInfo.wechatQr && contactInfo.wechatQrVisible)

      if (!hasOtherVisible) {
        wx.showToast({
          title: '至少需要一种联系方式',
          icon: 'none',
          duration: 2000
        })
        return
      }
    }

    this.setData({
      'contactInfo.mobileVisible': isVisible
    })
    this.initFormValidation()
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { title, content, category, publishType, endTime } = this.data.formData
    const { contactInfo } = this.data

    // 添加联系方式验证 - 至少选择一种联系方式且设置为可见
    const isContactValid = (contactInfo.mobile && contactInfo.mobileVisible) ||
      (contactInfo.wechatId && contactInfo.wechatVisible) ||
      (contactInfo.wechatQr && contactInfo.wechatQrVisible)

    const canSubmit = title.trim() && content.trim() && category && publishType && endTime && isContactValid
    this.setData({ canSubmit })
  },

  /**
   * 提交表单
   */
  onSubmit() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善信息',
        icon: 'none'
      })
      return
    }

    this.showPublishConfirm()
  },

  /**
   * 显示发布确认对话框
   */
  showPublishConfirm() {
    this.setData({
      showPublishDialog: true
    })
  },

  /**
   * 关闭发布确认对话框
   */
  onPublishDialogClose() {
    this.setData({
      showPublishDialog: false
    })
  },

  /**
   * 确认发布
   */
  onPublishConfirm() {
    this.setData({
      showPublishDialog: false
    })
    this.submitForm()
  },

  /**
   * 显示微信号编辑弹窗
   */
  showWechatDialog() {
    this.setData({
      showWechatDialog: true,
      wechatField: this.data.contactInfo.wechatId || '',
      showContactPicker: false
    })
  },

  /**
   * 关闭微信号编辑弹窗
   */
  onCloseWechatDialog() {
    this.setData({
      showWechatDialog: false,
      wechatField: ''
    })
  },

  /**
   * 微信号输入变化
   */
  onWechatFieldChange(e) {
    this.setData({
      wechatField: e.detail
    })
  },

  /**
   * 确认微信号
   */
  onConfirmWechat() {
    const wechatId = this.data.wechatField.trim()
    if (!wechatId) {
      wx.showToast({
        title: '请输入微信号',
        icon: 'none'
      })
      return
    }

    this.setData({
      'contactInfo.wechatId': wechatId,
      'contactInfo.wechatVisible': true,
      showWechatDialog: false,
      wechatField: ''
    })
    this.initFormValidation()
  },

  /**
   * 微信号可见性变更
   */
  onWechatVisibilityChange(e) {
    const isVisible = e.detail

    if (!isVisible) {
      const { contactInfo } = this.data
      const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
        (contactInfo.wechatQr && contactInfo.wechatQrVisible)

      if (!hasOtherVisible) {
        wx.showToast({
          title: '至少需要一种联系方式',
          icon: 'none',
          duration: 2000
        })
        return
      }
    }

    this.setData({
      'contactInfo.wechatVisible': isVisible
    })
    this.initFormValidation()
  },

  /**
   * 显示微信二维码编辑弹窗
   */
  showWechatQrDialog() {
    this.setData({
      showWechatQrDialog: true,
      tempQrCodePath: this.data.contactInfo.wechatQr || '',
      showContactPicker: false
    })
  },

  /**
   * 关闭微信二维码编辑弹窗
   */
  onCloseWechatQrDialog() {
    this.setData({
      showWechatQrDialog: false,
      tempQrCodePath: ''
    })
  },

  /**
   * 选择二维码图片
   */
  chooseQrCode() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          tempQrCodePath: res.tempFilePaths[0]
        })
      }
    })
  },

  /**
   * 确认微信二维码
   */
  async onConfirmWechatQr() {
    const qrPath = this.data.tempQrCodePath
    if (!qrPath) {
      wx.showToast({
        title: '请选择二维码图片',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({ title: '上传中...' })

      // 上传图片到服务器
      const uploadedFile = await util.uploadFile({
        filePath: qrPath,
        type: 6 // 使用类型6表示微信二维码
      })

      if (!uploadedFile || !uploadedFile.filePath) {
        throw new Error('文件上传失败，未获取到文件路径')
      }

      // 使用服务器返回的URL，同时保留本地预览路径
      this.setData({
        'contactInfo.wechatQr': util.formatImageUrl(uploadedFile.filePath),
        'contactInfo.wechatQrVisible': true,
        showWechatQrDialog: false,
        tempQrCodePath: ''
      })

      wx.hideLoading()
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
      this.initFormValidation()
    } catch (error) {
      wx.hideLoading()
      console.error('上传二维码失败:', error)
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
    }
  },

  /**
   * 微信二维码可见性变更
   */
  onWechatQrVisibilityChange(e) {
    const isVisible = e.detail

    if (!isVisible) {
      const { contactInfo } = this.data
      const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
        (contactInfo.wechatId && contactInfo.wechatVisible)

      if (!hasOtherVisible) {
        wx.showToast({
          title: '至少需要一种联系方式',
          icon: 'none',
          duration: 2000
        })
        return
      }
    }

    this.setData({
      'contactInfo.wechatQrVisible': isVisible
    })
    this.initFormValidation()
  },

  /**
   * 阻止事件冒泡
   */
  preventTap() {
    // 使用catch:tap已经阻止了事件冒泡，不需要额外操作
  },

  /**
   * 提交表单
   */
  async submitForm() {
    if (this.data.submitLoading) return

    this.setData({ submitLoading: true })

    try {
      // 构建联系方式JSON数据
      const contactData = []

      // 添加手机号联系方式
      if (this.data.contactInfo.mobile) {
        contactData.push({
          type: ContactType.MOBILE.code,
          value: this.data.contactInfo.mobile,
          visible: this.data.contactInfo.mobileVisible
        })
      }

      // 添加微信号联系方式
      if (this.data.contactInfo.wechatId) {
        contactData.push({
          type: ContactType.WECHAT_ID.code,
          value: this.data.contactInfo.wechatId,
          visible: this.data.contactInfo.wechatVisible
        })
      }

      // 添加微信二维码联系方式
      if (this.data.contactInfo.wechatQr) {
        // 如果是完整URL则直接使用，否则去掉前缀只保留路径部分
        let qrValue = this.data.contactInfo.wechatQr
        if (qrValue.startsWith(wx.getStorageSync('systemInfo').fileUrl)) {
          qrValue = qrValue.replace(wx.getStorageSync('systemInfo').fileUrl, '')
        }
        contactData.push({
          type: ContactType.WECHAT_QR.code,
          value: qrValue,
          visible: this.data.contactInfo.wechatQrVisible
        })
      }

      // 处理图片数据 - 使用服务器返回的filePath，存储为逗号分隔的字符串
      const images = this.data.fileList.map(file => file.filePath || file.url)

      const submitData = {
        title: this.data.formData.title,
        content: this.data.formData.content,
        category: this.data.formData.category,
        publishType: this.data.formData.publishType,
        endTime: this.data.formData.endTime,
        images: images.join(','), // 改为逗号分隔的字符串格式
        contactInfo: JSON.stringify(contactData)
      }

      // 根据是否是编辑模式调用不同的API
      if (this.data.isEdit) {
        submitData.id = this.data.helpId
        await api.updateCommunityHelp(submitData)
        // 设置刷新标记，让详情页面知道需要刷新
        wx.setStorageSync('needRefreshHelpDetail', true)
      } else {
        await api.publishCommunityHelp(submitData)
      }

      this.setData({ submitLoading: false })

      wx.showToast({
        title: this.data.isEdit ? '更新成功' : '发布成功',
        icon: 'success'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 100)
    } catch (error) {
      this.setData({ submitLoading: false })
      console.error('发布失败:', error)

      wx.showToast({
        title: '发布失败，请稍后重试',
        icon: 'none'
      })
    }
  }
})
